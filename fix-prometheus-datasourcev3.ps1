# PowerShell script to replace "datasource": "Prometheus", with proper object format
param(
    [string]$TargetDir = "infrastructure"
)

function Update-PrometheusDataSource {
    param([string]$FilePath)
    
    try {
        Write-Host "Processing: $FilePath"
        
        # Read the file content
        $content = Get-Content $FilePath -Raw -Encoding UTF8
        
        # Check if file contains patterns we want to replace
        $hasStringPattern = $content -match '"datasource":\s*"Prometheus"'
        $hasUidPattern = $content -match '"datasource":\s*\{\s*"type":\s*"prometheus",\s*"uid":\s*"prometheus"\s*\}'

        if ($hasStringPattern -or $hasUidPattern) {
            Write-Host "  Found Prometheus datasource references"

            $updatedContent = $content

            # Replace string pattern with proper object format
            if ($hasStringPattern) {
                Write-Host "    Replacing string datasource pattern"
                $updatedContent = $updatedContent -replace '"datasource":\s*"Prometheus"', '"datasource": {
        "type": "prometheus"
      }'
            }

            # Replace uid pattern by removing the uid property
            if ($hasUidPattern) {
                Write-Host "    Replacing datasource with uid pattern"
                $updatedContent = $updatedContent -replace '"datasource":\s*\{\s*"type":\s*"prometheus",\s*"uid":\s*"prometheus"\s*\}', '"datasource": {
            "type": "prometheus"
          }'
            }
            
            # Validate that it's still valid JSON by trying to parse it
            try {
                $null = $updatedContent | ConvertFrom-Json
                Write-Host "  JSON validation passed"
            }
            catch {
                Write-Warning "  JSON validation failed for $FilePath - skipping"
                return $false
            }
            
            # Write back as UTF-8 without BOM
            $utf8NoBom = New-Object System.Text.UTF8Encoding $false
            [System.IO.File]::WriteAllText($FilePath, $updatedContent, $utf8NoBom)
            
            Write-Host "  Successfully updated: $FilePath" -ForegroundColor Green
            return $true
        }
        else {
            Write-Host "  No Prometheus datasource references found"
            return $false
        }
    }
    catch {
        Write-Warning "Error processing file $FilePath : $($_.Exception.Message)"
        return $false
    }
}

function Process-Directory {
    param([string]$Directory)
    
    Write-Host "Scanning directory: $Directory" -ForegroundColor Yellow
    
    # Find all dashboard.json files recursively
    $jsonFiles = Get-ChildItem -Path $Directory -Name "dashboard.json" -Recurse
    
    $processedCount = 0
    $updatedCount = 0
    
    foreach ($file in $jsonFiles) {
        $fullPath = Join-Path $Directory $file
        $processedCount++
        
        if (Update-PrometheusDataSource $fullPath) {
            $updatedCount++
        }
    }
    
    Write-Host "`nSummary:" -ForegroundColor Cyan
    Write-Host "Processed: $processedCount files"
    Write-Host "Updated: $updatedCount files"
}

# Main execution
if (Test-Path $TargetDir) {
    Write-Host "Starting Prometheus datasource update..." -ForegroundColor Cyan
    Process-Directory $TargetDir
    Write-Host "`nPrometheus datasource update completed!" -ForegroundColor Green
} else {
    Write-Error "Target directory not found: $TargetDir"
}
